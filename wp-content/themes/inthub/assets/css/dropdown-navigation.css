/**
 * Multi-Level Dropdown Navigation Styles for IntHub Theme
 * 
 * @package IntHub
 * @since 1.0.0
 */

/* ==========================================================================
   Base Navigation Styles
   ========================================================================== */

.main-navigation {
    position: relative;
    z-index: 1000;
}

.main-navigation ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-navigation li {
    position: relative;
}

/* ==========================================================================
   Dropdown Menu Base Styles
   ========================================================================== */

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 220px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    padding: 0.5rem 0;
}

.dropdown:hover > .dropdown-menu,
.dropdown.active > .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* ==========================================================================
   Multi-Level Dropdown Positioning
   ========================================================================== */

/* Second level dropdowns */
.dropdown-submenu > .dropdown-menu {
    top: 0;
    left: 100%;
    margin-left: 0.5rem;
}

/* Third level dropdowns */
.dropdown-menu-level-3 {
    top: 0;
    left: 100%;
    margin-left: 0.5rem;
}

/* Prevent dropdowns from going off-screen */
.dropdown-submenu:hover > .dropdown-menu {
    display: block;
}

/* Right-aligned dropdowns for items near screen edge */
.dropdown.dropdown-right > .dropdown-menu {
    left: auto;
    right: 0;
}

.dropdown-submenu.dropdown-left > .dropdown-menu {
    left: auto;
    right: 100%;
    margin-left: 0;
    margin-right: 0.5rem;
}

/* ==========================================================================
   Dropdown Items Styling
   ========================================================================== */

.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.75rem 1.25rem;
    color: #374151;
    text-decoration: none;
    font-size: 0.875rem;
    line-height: 1.25rem;
    border: none;
    background: transparent;
    transition: all 0.2s ease;
}

.dropdown-item:hover,
.dropdown-item:focus {
    color: #ec4899;
    background-color: #f9fafb;
    text-decoration: none;
}

.dropdown-item.active {
    color: #ec4899;
    background-color: #fdf2f8;
}

/* ==========================================================================
   Dropdown Arrows
   ========================================================================== */

.dropdown-arrow,
.dropdown-arrow-sub {
    display: inline-flex;
    align-items: center;
    transition: transform 0.2s ease;
}

.dropdown:hover .dropdown-arrow svg {
    transform: rotate(180deg);
}

.dropdown-submenu:hover .dropdown-arrow-sub svg {
    transform: rotate(-90deg);
}

/* ==========================================================================
   Mobile Responsive Styles
   ========================================================================== */

@media (max-width: 768px) {
    /* Mobile accordion-style dropdowns */
    .mobile-navigation .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        border-radius: 0;
        background: #f8fafc;
        margin-left: 1rem;
        margin-top: 0.5rem;
        padding: 0;
        display: none;
    }
    
    .mobile-navigation .dropdown.active > .dropdown-menu {
        display: block;
    }
    
    .mobile-navigation .dropdown-item {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .mobile-navigation .dropdown-item:last-child {
        border-bottom: none;
    }
    
    /* Mobile dropdown arrows */
    .mobile-navigation .dropdown-arrow svg {
        transform: rotate(0deg);
    }
    
    .mobile-navigation .dropdown.active .dropdown-arrow svg {
        transform: rotate(180deg);
    }
    
    .mobile-navigation .dropdown-arrow-sub svg {
        transform: rotate(0deg);
    }
    
    .mobile-navigation .dropdown-submenu.active .dropdown-arrow-sub svg {
        transform: rotate(90deg);
    }
}

/* ==========================================================================
   Accessibility Enhancements
   ========================================================================== */

/* Focus styles for keyboard navigation */
.nav-link:focus,
.dropdown-item:focus {
    outline: 2px solid #ec4899;
    outline-offset: 2px;
}

/* Screen reader only text */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ==========================================================================
   Animation Enhancements
   ========================================================================== */

/* Staggered animation for dropdown items */
.dropdown-menu li {
    opacity: 0;
    transform: translateY(-5px);
    transition: all 0.2s ease;
}

.dropdown:hover .dropdown-menu li,
.dropdown.active .dropdown-menu li {
    opacity: 1;
    transform: translateY(0);
}

.dropdown:hover .dropdown-menu li:nth-child(1) { transition-delay: 0.05s; }
.dropdown:hover .dropdown-menu li:nth-child(2) { transition-delay: 0.1s; }
.dropdown:hover .dropdown-menu li:nth-child(3) { transition-delay: 0.15s; }
.dropdown:hover .dropdown-menu li:nth-child(4) { transition-delay: 0.2s; }
.dropdown:hover .dropdown-menu li:nth-child(5) { transition-delay: 0.25s; }

/* ==========================================================================
   Dark Mode Support
   ========================================================================== */

@media (prefers-color-scheme: dark) {
    .dropdown-menu {
        background: #1f2937;
        border-color: #374151;
    }
    
    .dropdown-item {
        color: #e5e7eb;
    }
    
    .dropdown-item:hover,
    .dropdown-item:focus {
        color: #ec4899;
        background-color: #374151;
    }
    
    .dropdown-item.active {
        background-color: #1f2937;
    }
    
    .mobile-navigation .dropdown-menu {
        background: #374151;
    }
    
    .mobile-navigation .dropdown-item {
        border-color: #4b5563;
    }
}

/* ==========================================================================
   High Contrast Mode Support
   ========================================================================== */

@media (prefers-contrast: high) {
    .dropdown-menu {
        border-width: 2px;
        border-color: #000000;
    }
    
    .dropdown-item:hover,
    .dropdown-item:focus {
        background-color: #000000;
        color: #ffffff;
    }
}

/* ==========================================================================
   Reduced Motion Support
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
    .dropdown-menu,
    .dropdown-arrow,
    .dropdown-arrow-sub,
    .dropdown-item,
    .dropdown-menu li {
        transition: none;
    }
    
    .dropdown:hover .dropdown-menu,
    .dropdown.active .dropdown-menu {
        transform: none;
    }
}
