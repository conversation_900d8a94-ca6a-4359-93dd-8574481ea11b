/**
 * Navigation JavaScript for IntHub theme
 * 
 * @package IntHub
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {
        initNavigation();
    });

    /**
     * Initialize navigation functionality
     */
    function initNavigation() {
        initMobileMenu();
        initStickyHeader();
        initActiveMenuItems();
        initDropdownMenus();
    }

    /**
     * Initialize mobile menu
     */
    function initMobileMenu() {
        var $mobileToggle = $('.mobile-menu-toggle');
        var $mobileNav = $('#mobile-navigation');
        var $body = $('body');
        var isOpen = false;

        // Toggle mobile menu
        $mobileToggle.on('click', function(e) {
            e.preventDefault();
            toggleMobileMenu();
        });

        // Close mobile menu when clicking outside
        $(document).on('click', function(e) {
            if (isOpen && !$(e.target).closest('.site-header').length) {
                closeMobileMenu();
            }
        });

        // Close mobile menu on escape key
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27 && isOpen) { // Escape key
                closeMobileMenu();
            }
        });

        // Close mobile menu when window is resized to desktop
        $(window).on('resize', function() {
            if ($(window).width() >= 768 && isOpen) {
                closeMobileMenu();
            }
        });

        // Handle mobile menu links
        $mobileNav.find('a').on('click', function() {
            // Close menu when clicking on anchor links
            if ($(this).attr('href').indexOf('#') !== -1) {
                setTimeout(closeMobileMenu, 300);
            }
        });

        function toggleMobileMenu() {
            if (isOpen) {
                closeMobileMenu();
            } else {
                openMobileMenu();
            }
        }

        function openMobileMenu() {
            isOpen = true;
            $mobileNav.removeClass('hidden').addClass('block');
            $mobileToggle.attr('aria-expanded', 'true');
            $body.addClass('mobile-menu-open');
            
            // Change hamburger to X
            updateMobileToggleIcon(true);
            
            // Animate menu items
            $mobileNav.find('li').each(function(index) {
                $(this).css({
                    'opacity': '0',
                    'transform': 'translateY(-10px)'
                }).delay(index * 50).animate({
                    'opacity': '1'
                }, 200, function() {
                    $(this).css('transform', 'translateY(0)');
                });
            });
        }

        function closeMobileMenu() {
            isOpen = false;
            $mobileNav.removeClass('block').addClass('hidden');
            $mobileToggle.attr('aria-expanded', 'false');
            $body.removeClass('mobile-menu-open');
            
            // Change X back to hamburger
            updateMobileToggleIcon(false);
        }

        function updateMobileToggleIcon(isOpen) {
            var $icon = $mobileToggle.find('svg');
            if (isOpen) {
                $icon.html('<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>');
            } else {
                $icon.html('<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>');
            }
        }
    }

    /**
     * Initialize sticky header
     */
    function initStickyHeader() {
        var $header = $('.site-header');
        var $window = $(window);
        var headerHeight = $header.outerHeight();
        var scrollThreshold = 100;
        var isSticky = false;

        function updateStickyHeader() {
            var scrollTop = $window.scrollTop();
            
            if (scrollTop > scrollThreshold && !isSticky) {
                isSticky = true;
                $header.addClass('header-sticky');
                $('body').css('padding-top', headerHeight + 'px');
            } else if (scrollTop <= scrollThreshold && isSticky) {
                isSticky = false;
                $header.removeClass('header-sticky');
                $('body').css('padding-top', '0');
            }
        }

        // Throttle scroll events for better performance
        var throttledUpdate = throttle(updateStickyHeader, 10);
        $window.on('scroll', throttledUpdate);

        // Update on resize
        $window.on('resize', function() {
            headerHeight = $header.outerHeight();
            if (isSticky) {
                $('body').css('padding-top', headerHeight + 'px');
            }
        });

        // Add CSS for sticky header
        if (!$('#sticky-header-styles').length) {
            $('<style id="sticky-header-styles">')
                .text(`
                    .site-header {
                        transition: all 0.3s ease;
                    }
                    .site-header.header-sticky {
                        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
                        backdrop-filter: blur(10px);
                        background: rgba(255, 255, 255, 0.95);
                    }
                    body.mobile-menu-open {
                        overflow: hidden;
                    }
                `)
                .appendTo('head');
        }
    }

    /**
     * Initialize active menu items
     */
    function initActiveMenuItems() {
        var currentUrl = window.location.href;
        var currentPath = window.location.pathname;

        // Mark current page menu item as active
        $('.main-navigation a, #mobile-navigation a').each(function() {
            var $link = $(this);
            var href = $link.attr('href');

            if (href === currentUrl || href === currentPath || 
                (href !== '#' && currentUrl.indexOf(href) !== -1)) {
                $link.addClass('current-menu-item');
                $link.closest('li').addClass('current-menu-item');
            }
        });

        // Handle section highlighting for single page navigation
        if ($('body').hasClass('home-page')) {
            initSectionHighlighting();
        }
    }

    /**
     * Initialize section highlighting for single page navigation
     */
    function initSectionHighlighting() {
        var $sections = $('section[id]');
        var $navLinks = $('.main-navigation a[href*="#"], #mobile-navigation a[href*="#"]');
        var headerHeight = $('.site-header').outerHeight() || 0;

        function updateActiveSection() {
            var scrollTop = $(window).scrollTop() + headerHeight + 50;
            var currentSection = '';

            $sections.each(function() {
                var $section = $(this);
                var sectionTop = $section.offset().top;
                var sectionBottom = sectionTop + $section.outerHeight();

                if (scrollTop >= sectionTop && scrollTop < sectionBottom) {
                    currentSection = $section.attr('id');
                }
            });

            // Update active nav links
            $navLinks.removeClass('active-section');
            if (currentSection) {
                $navLinks.filter('[href*="#' + currentSection + '"]').addClass('active-section');
            }
        }

        // Throttle scroll events
        var throttledUpdate = throttle(updateActiveSection, 100);
        $(window).on('scroll', throttledUpdate);

        // Initial update
        updateActiveSection();
    }

    /**
     * Initialize enhanced multi-level dropdown menus
     */
    function initDropdownMenus() {
        var $dropdownItems = $('.menu-item-has-children');
        var $dropdownToggles = $('.menu-item-has-children > a');
        var activeDropdowns = [];
        var hoverTimeout;

        // Initialize each dropdown item
        $dropdownItems.each(function() {
            var $item = $(this);
            var $toggle = $item.children('a');
            var $submenu = $item.children('.sub-menu, .dropdown-menu');

            if ($submenu.length) {
                // Add ARIA attributes for accessibility
                $toggle.attr({
                    'aria-haspopup': 'true',
                    'aria-expanded': 'false'
                });

                $submenu.attr('aria-hidden', 'true');

                // Position submenu for screen edge detection
                positionSubmenu($item, $submenu);
            }
        });

        // Desktop hover behavior with delay
        $dropdownItems.on('mouseenter', function() {
            if ($(window).width() >= 768) {
                var $item = $(this);
                var $submenu = $item.children('.sub-menu, .dropdown-menu');
                var $toggle = $item.children('a');

                clearTimeout(hoverTimeout);

                // Close other dropdowns at the same level
                $item.siblings('.dropdown.active').removeClass('active')
                    .children('a').attr('aria-expanded', 'false')
                    .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true');

                // Show current dropdown
                $item.addClass('active');
                $toggle.attr('aria-expanded', 'true');
                $submenu.attr('aria-hidden', 'false');

                // Reposition if needed
                positionSubmenu($item, $submenu);
            }
        }).on('mouseleave', function() {
            if ($(window).width() >= 768) {
                var $item = $(this);
                var $toggle = $item.children('a');
                var $submenu = $item.children('.sub-menu, .dropdown-menu');

                hoverTimeout = setTimeout(function() {
                    $item.removeClass('active');
                    $toggle.attr('aria-expanded', 'false');
                    $submenu.attr('aria-hidden', 'true');
                }, 150);
            }
        });

        // Mobile click behavior
        $dropdownToggles.on('click', function(e) {
            if ($(window).width() < 768) {
                e.preventDefault();
                var $toggle = $(this);
                var $item = $toggle.parent();
                var $submenu = $item.children('.sub-menu, .dropdown-menu');
                var isActive = $item.hasClass('active');

                if (isActive) {
                    // Close dropdown
                    $item.removeClass('active');
                    $toggle.attr('aria-expanded', 'false');
                    $submenu.attr('aria-hidden', 'true').slideUp(200);
                } else {
                    // Close siblings
                    $item.siblings('.active').removeClass('active')
                        .children('a').attr('aria-expanded', 'false')
                        .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true').slideUp(200);

                    // Open current dropdown
                    $item.addClass('active');
                    $toggle.attr('aria-expanded', 'true');
                    $submenu.attr('aria-hidden', 'false').slideDown(200);
                }
            }
        });

        // Keyboard navigation
        $dropdownToggles.on('keydown', function(e) {
            var $toggle = $(this);
            var $item = $toggle.parent();
            var $submenu = $item.children('.sub-menu, .dropdown-menu');

            switch(e.keyCode) {
                case 13: // Enter
                case 32: // Space
                    if ($submenu.length) {
                        e.preventDefault();
                        if ($(window).width() >= 768) {
                            // Focus first submenu item
                            $submenu.find('a').first().focus();
                        } else {
                            // Toggle mobile dropdown
                            $toggle.trigger('click');
                        }
                    }
                    break;

                case 27: // Escape
                    if ($item.hasClass('active')) {
                        $item.removeClass('active');
                        $toggle.attr('aria-expanded', 'false').focus();
                        $submenu.attr('aria-hidden', 'true');
                    }
                    break;

                case 40: // Down arrow
                    if ($submenu.length && $item.hasClass('active')) {
                        e.preventDefault();
                        $submenu.find('a').first().focus();
                    }
                    break;
            }
        });

        // Submenu keyboard navigation
        $('.sub-menu a, .dropdown-menu a').on('keydown', function(e) {
            var $link = $(this);
            var $submenu = $link.closest('.sub-menu, .dropdown-menu');
            var $links = $submenu.find('a');
            var currentIndex = $links.index($link);

            switch(e.keyCode) {
                case 27: // Escape
                    e.preventDefault();
                    var $parentToggle = $submenu.siblings('a');
                    $submenu.closest('.menu-item-has-children').removeClass('active');
                    $parentToggle.attr('aria-expanded', 'false').focus();
                    $submenu.attr('aria-hidden', 'true');
                    break;

                case 38: // Up arrow
                    e.preventDefault();
                    if (currentIndex > 0) {
                        $links.eq(currentIndex - 1).focus();
                    } else {
                        $submenu.siblings('a').focus();
                    }
                    break;

                case 40: // Down arrow
                    e.preventDefault();
                    if (currentIndex < $links.length - 1) {
                        $links.eq(currentIndex + 1).focus();
                    }
                    break;
            }
        });

        // Close dropdowns when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.menu-item-has-children').length) {
                $('.menu-item-has-children.active').removeClass('active')
                    .children('a').attr('aria-expanded', 'false')
                    .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true');
            }
        });

        // Position submenu to prevent overflow
        function positionSubmenu($item, $submenu) {
            if ($(window).width() >= 768) {
                var itemOffset = $item.offset();
                var submenuWidth = $submenu.outerWidth();
                var windowWidth = $(window).width();
                var isSubSubmenu = $item.closest('.sub-menu, .dropdown-menu').length > 0;

                // Reset positioning classes
                $item.removeClass('dropdown-right dropdown-left');

                if (isSubSubmenu) {
                    // For sub-submenus, check if they would go off the right edge
                    if (itemOffset.left + $item.outerWidth() + submenuWidth > windowWidth - 20) {
                        $item.addClass('dropdown-left');
                    }
                } else {
                    // For main dropdowns, check if they would go off the right edge
                    if (itemOffset.left + submenuWidth > windowWidth - 20) {
                        $item.addClass('dropdown-right');
                    }
                }
            }
        }

        // Reposition on window resize
        $(window).on('resize', function() {
            if ($(window).width() >= 768) {
                $('.menu-item-has-children.active').each(function() {
                    var $item = $(this);
                    var $submenu = $item.children('.sub-menu, .dropdown-menu');
                    positionSubmenu($item, $submenu);
                });
            } else {
                // Reset mobile state
                $('.menu-item-has-children').removeClass('active dropdown-right dropdown-left')
                    .children('a').attr('aria-expanded', 'false')
                    .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true').removeAttr('style');
            }
        });
    }

    /**
     * Utility function to throttle events
     */
    function throttle(func, limit) {
        var inThrottle;
        return function() {
            var args = arguments;
            var context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(function() {
                    inThrottle = false;
                }, limit);
            }
        };
    }

    // Expose navigation functions globally if needed
    window.IntHubNavigation = {
        closeMobileMenu: function() {
            $('.mobile-menu-toggle').trigger('click');
        },
        closeAllDropdowns: function() {
            $('.menu-item-has-children.active').removeClass('active')
                .children('a').attr('aria-expanded', 'false')
                .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true');
        },
        openDropdown: function($item) {
            if ($item.hasClass('menu-item-has-children')) {
                var $toggle = $item.children('a');
                var $submenu = $item.children('.sub-menu, .dropdown-menu');

                $item.addClass('active');
                $toggle.attr('aria-expanded', 'true');
                $submenu.attr('aria-hidden', 'false');
            }
        }
    };

})(jQuery);
